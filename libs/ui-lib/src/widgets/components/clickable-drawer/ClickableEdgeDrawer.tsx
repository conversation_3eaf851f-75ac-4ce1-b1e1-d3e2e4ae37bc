import { PeekB<PERSON>, PeekClickAwayListener, PeekDivider, PeekStack } from "@piiqtechnologies/ui-components";
import { useAnalytics } from "@utils";
import React, { useRef, useState } from "react";

const drawerBleeding = 72;

interface ClickableEdgeDrawerProps {
  children: React.ReactNode;
  DrawerBleedingContent: React.FC;
  eventIdentification?: string;
  customBleedingArea?: number;
  onToggle?: (newOpen: boolean) => void;
}

export default function ClickableEdgeDrawer({
  children,
  DrawerBleedingContent,
  eventIdentification,
  customBleedingArea,
  onToggle,
}: ClickableEdgeDrawerProps) {

  const bleedingArea = customBleedingArea || drawerBleeding;
  const analytics = useAnalytics();
  const [open, setOpen] = useState(false);
  const ref = useRef(null);

  const handleToggle = (newOpen: boolean) => () => {
    setOpen(newOpen);
    onToggle?.(newOpen);
    if (newOpen) {
      analytics?.track(`${eventIdentification}_opened`);
    }
  };

  return (
    <PeekClickAwayListener onClickAway={handleToggle(false)}>
      <PeekStack
        direction={"column"}
        sx={{
          position: "absolute",
          top: `calc(100vh - ${bleedingArea}px)`,
          right: 0,
          bottom: 0,
          left: 0,
        }}
      >
        <PeekBox
          onClick={handleToggle(!open)}
          sx={{
            position: "absolute",
            visibility: "visible",
            borderTopLeftRadius: 12,
            borderTopRightRadius: 12,
            right: 0,
            left: 0,
            backgroundColor: "common.white",
            boxShadow: "0px -2px 4px #ADADAD21",
          }}
        >
          <DrawerBleedingContent />
        </PeekBox>
        <PeekDivider light orientation="horizontal" flexItem sx={{ height: "5px" }} />
        <PeekBox
          ref={ref}
          sx={{
            p: 2,
            height: "100%",
            overflow: "auto",
            backgroundColor: "common.white",
          }}
        >
          {children}
        </PeekBox>
      </PeekStack>
    </PeekClickAwayListener>
  );
}

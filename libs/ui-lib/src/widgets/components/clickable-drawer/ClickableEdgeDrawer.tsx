import { PeekB<PERSON>, PeekClick<PERSON>wayListener, PeekDivider, PeekStack } from "@piiqtechnologies/ui-components";
import { useAnalytics } from "@utils";
import React, { useEffect, useRef, useState } from "react";

const drawerBleeding = 72;

interface ClickableEdgeDrawerProps {
  children: React.ReactNode;
  DrawerBleedingContent: React.FC;
  eventIdentification?: string;
  customBleedingArea?: number;
  onToggle?: (newOpen: boolean) => void;
}

export default function ClickableEdgeDrawer({
  children,
  DrawerBleedingContent,
  eventIdentification,
  customBleedingArea,
  onToggle,
}: ClickableEdgeDrawerProps) {

  const bleedingArea = customBleedingArea || drawerBleeding;
  const analytics = useAnalytics();
  const [open, setOpen] = useState(false);
  const [refHeight, setRefHeight] = useState(0);
  const ref = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (ref.current) {
      setRefHeight(ref.current.offsetHeight);
    }
  }, [open, children]);

  const handleToggle = (newOpen: boolean) => () => {
    setOpen(newOpen);
    onToggle?.(newOpen);
    if (newOpen) {
      analytics?.track(`${eventIdentification}_opened`);
    }
  };

  return (
    <>
      {/* BACKGROUND */}
      {open && <PeekBox
        sx={{
          height: "100%",
          width: "100%",
          opacity: 0.5,
          backgroundColor: "black",
          position: "absolute",
          top: 0,
          left: 0,
          mt: "0px !important",
        }}
      />}
      {/* DRAWER */}
      <PeekClickAwayListener onClickAway={handleToggle(false)}>
        <PeekStack
          direction={"column"}
          sx={{
            position: "absolute",
            maxHeight: "80vh",
            right: 0,
            left: 0,
            bottom: 0,
            overflow: "auto",
            transition: "max-height 0.3s ease-in-out !important",
            boxShadow: "0px -2px 4px #ADADAD21",
            backgroundColor: "common.white",
            borderTopLeftRadius: 12,
            borderTopRightRadius: 12,
          }}
        >
          <PeekBox onClick={handleToggle(!open)}>
            <DrawerBleedingContent />
          </PeekBox>
          {open && <>
            <PeekDivider light orientation="horizontal" flexItem sx={{ height: "5px" }} />
            <PeekBox
              ref={ref}
              sx={{
                p: 2,
                height: "fit-content",
                backgroundColor: "common.white",
              }}
            >
              {children}
            </PeekBox>
          </>
          }
        </PeekStack>
      </PeekClickAwayListener>
    </>
  );
}
